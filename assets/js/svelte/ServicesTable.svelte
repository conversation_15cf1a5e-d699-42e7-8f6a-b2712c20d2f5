<script lang="ts">
	import { preventDefault } from 'svelte/legacy';

	import { onMount, tick } from "svelte";
	import { fade } from "svelte/transition";
	import Fuse from "fuse.js";
    import type IFuseOptions from "fuse.js";
	import { Tooltip } from "bootstrap";
	import {debounce, defaultTo, flatMap, intersectionBy, orderBy, sumBy, uniqBy} from "lodash";

	// TanStack Table imports
	import { writable } from 'svelte/store';
	import {
		createSvelteTable,
		flexRender,
		getCoreRowModel,
		getSortedRowModel,
		getFilteredRowModel,
		renderComponent
	} from '@tanstack/svelte-table';
	import type { ColumnDef, TableOptions, SortingState, ColumnFiltersState, FilterFn } from '@tanstack/svelte-table';

	import { getServices } from "../api";
	import { formatBytes } from "../utils";
	import {mercureEventSource, waitingForUpdates, selectedServices} from "./stores";

	// Components
	import Image from "./Cells/Image.svelte";
	import Urls from "./Cells/Urls.svelte";
	import Replicas from "./Cells/Replicas.svelte";
	import DetailsModal from "./DetailsModal.svelte";
	import RedeployModal from "./RedeployModal.svelte";
	import Details from "./Cells/Details.svelte";
	import UpdateBar from "./UpdateBar.svelte";
	import Stack from "./Cells/Stack.svelte";
	import { default as ServiceComponent } from "./Cells/Service.svelte";
	import StatsTable from "./StatsTable.svelte";
	import CsvDownloadButton from "./CsvDownloadButton.svelte";

	let loading = $state(false);
	let services: Service[] = $state([]);
	let loadingErrors: object[] = $state([]);
	let modalOpen = $state(false);
	let modalService: Service | null = $state(null);
	let tagModalOpen = $state(false);
	let tagModalService: Service | null = $state(null);
	let eventSourceInitialized = false;
	let searchValue: string = $state("");

	// TanStack Table state
	let sorting = writable<SortingState>([]);
	let columnFilters = writable<ColumnFiltersState>([]);
	let globalFilter = $state("");

	onMount(async () => {
		if (services.length === 0) {
			await loadServices();
			let openedService = window.location.hash.replace("#", "");
			if (openedService && getService(openedService)) {
				showDetails(getService(openedService));
			}
		}
		await tick();
		await new Promise(resolve => setTimeout(resolve, 100));
		initTooltips();

		if (!eventSourceInitialized) {
			$mercureEventSource.addEventListener("message", event => {
				let message = JSON.parse(event.data);
				if (message.topic === "service") {
					const serviceKey = services.findIndex(
							service => service.id === message.data.id
					);
					if (serviceKey !== -1) {
						services[serviceKey] = message.data;
					}
				}
			});
			eventSourceInitialized = true;
		}

		// récupération de la valeur à rechercher dans les paramètres d'URL
		const searchParams = new URLSearchParams(window.location.search);
		if (searchParams.get("search")) {
			searchValue = searchParams.get("search") || "";
			globalFilter = searchValue;
		}
	});

	async function loadServices(force = false) {
		loading = true;
		const response = await getServices(force);
		services = response.services;
		loadingErrors = response.errors;
		fuse.setCollection(services);
		loading = false;
		return services;
	}

	function initTooltips() {
		const tooltipTriggerList = document.querySelectorAll(
			'[data-bs-toggle="tooltip"]'
		);
		[...tooltipTriggerList].map(
			tooltipTriggerEl => new Tooltip(tooltipTriggerEl)
		);
	}

	function getService(id: string): Service {
		return services.find(s => s.id === id)!;
	}

	function showDetails(service: Service) {
		modalOpen = true;
		modalService = service;
	}

	function showTags(service: Service) {
		tagModalOpen = true;
		tagModalService = service;
	}

	const columns: ColumnDef<Service>[] = [
		{
			id: "env",
			accessorKey: "env",
			header: "Env",
			cell: info => info.getValue(),
		},
		{
			id: "server",
			accessorKey: "server",
			header: "Cluster",
			cell: info => info.getValue(),
		},
		{
			id: "stack",
			accessorKey: "stack",
			header: "Stack",
			cell: (props) => renderComponent(Stack, {
				service: props.row.original,
				services: () => services,
				onclick: (stack: string) => {
					searchValue = stack;
					// On utilisera table.setGlobalFilter après la création de la table
					globalFilter = stack;
					frontWebOnly = false;
				},
			}),
		},
		{
			id: "name",
			accessorKey: "name",
			header: "Service",
			cell: (props) => renderComponent(ServiceComponent, {
				service: props.row.original,
			}),
		},
		{
			id: "urls",
			accessorFn: (service: Service) => service.repository.image + " " + service.repository.imageDigest,
			header: "Urls",
			enableSorting: false,
			cell: (props) => renderComponent(Urls, {
				service: props.row.original,
			}),
		},
		{
			id: "images",
			accessorFn: (service: Service) => service.urls.join(" ") + " " + service.middleware,
			header: "Images",
			enableSorting: false,
			cell: (props) => renderComponent(Image, {
				service: props.row.original,
			}),
		},
		{
			id: "replicas",
			accessorFn: (service: Service) => service.status.tasks.running,
			header: "Replicas",
			cell: (props) => renderComponent(Replicas, {
				service: props.row.original,
			}),
		},
		{
			id: "php",
			accessorFn: (service: Service) => {
				if (service?.details?.php?.version) {
					return service.details.php.version.split('.').map( n => +n+100000 ).join('.');
				}
				return service.redeployedAllowed ? "000000.000000.000001" : "000000.000000.000000";
			},
			header: '<div class="text-center"><i class="fa-brands fa-php fs-4"></i></div>',
            meta: {
                cellHTML: true
            },
            cell: (info) => {
				const service = info.row.original;
				if (service.redeployedAllowed) {
					let color = "secondary";

					if (service?.details?.php?.latest_patch_version) {
						color = "success";
						if (!service?.details?.php?.is_latest) {
							color = "warning text-dark";
						}
						if (service?.details?.php?.is_eoled) {
							color = "danger";
						}
					}

					if (service?.details?.php?.version) {
						return `<div class="text-end"><span class="badge bg-${color}">${service.details.php.version}</span></div>`;
					} else if (service.details) {
						return "";
					} else {
						return `<div class="text-end">…</div>`;
					}
				}
				return "";
			},
		},
		{
			id: "symfony",
			accessorFn: (service: Service) => {
				if (service?.details?.symfony?.version) {
					return service.details.symfony.version.split('.').map( n => +n+100000 ).join('.');
				}
				return service.redeployedAllowed ? "000000.000000.000001" : "000000.000000.000000";
			},
			header: '<div class="text-center"><i class="fa-brands fa-symfony fs-5"></i></div>',
            meta: {
                cellHTML: true
            },
			cell: (info) => {
				const service = info.row.original;
				if (service.redeployedAllowed) {
					let color = "secondary";

					if (service?.details?.symfony?.latest_patch_version) {
						color = "success";
						if (service?.details?.symfony?.latest_patch_version !== service?.details?.symfony?.version) {
							color = "warning text-dark";
						}
						if (service?.details?.symfony.is_eoled) {
							color = "danger";
						}
					}

					if (service?.details?.symfony?.version) {
						return `<div class="text-end"><span class="badge bg-${color}">${service.details.symfony.version}</span></div>`;
					} else if (service.details) {
						return "";
					} else {
						return `<div class="text-end">…</div>`;
					}
				}
				return "";
			},
		},
		{
			id: "security",
			accessorFn: (service: Service) => {
				if (
					service.details === null ||
					!service.details.hasOwnProperty("local-php-security-checker") ||
					service.details["local-php-security-checker"] === null
				) {
					return -1;
				}
				return Object.entries(service.details["local-php-security-checker"])
					.length;
			},
			header: "Sécu",
            meta: {
                cellHTML: true
            },
			cell: (info) => {
				const service = info.row.original;
				if (service.details && service.details["local-php-security-checker"]) {
					let length = flatMap(service.details["local-php-security-checker"], 'advisories').length;
					return `<div class="text-center fw-bold">${
						length
							? `<i class="fa-solid fa-shield-xmark text-danger me-1"></i>${
									length
							  }`
							: `<i class="fa-solid fa-shield-check text-success"></i>`
					}</div>`;
				}
				return `<div class="text-end">${service.redeployedAllowed && service.details === null ? "…" : ""}</div>`;
			},
		},
		{
			id: "built",
			accessorFn: (service: Service) => service?.details?.php?.buildDate ? new Date(service.details.php.buildDate) : null,
			header: "Built",
            meta: {
                cellHTML: true
            },
			cell: (info) => {
				const service = info.row.original;
				if (!service?.details?.php?.buildDate) {
					return "";
				}
				let date = new Date(service.details.php.buildDate).toLocaleDateString("fr-FR", {
					day: '2-digit',
					month: '2-digit',
					year: '2-digit'
				});
				return `<div class="text-end">${date}</div>`;
			},
		},
		{
			id: "updatedAt",
			accessorFn: (service: Service) => new Date(service.updatedAt),
			header: "Updated",
            meta: {
                cellHTML: true
            },
			cell: (info) => {
				const service = info.row.original;
				let date = new Date(service.updatedAt).toLocaleDateString("fr-FR", {
					day: '2-digit',
					month: '2-digit',
					year: '2-digit'
				});
				let commit = null;
				if (service?.details?.git?.lastCommit) {
					commit = "#" + service.details.git.lastCommit.slice(0, 8);
				}
				return `<div class="text-end">${date}${commit ? `<br>${commit}` : ""}</div>`;
			},
		},
		{
			id: "details",
			accessorKey: "id", // Dummy accessor since we don't sort on this
			header: "Détails",
			enableSorting: false,
			cell: (props) => renderComponent(Details, {
				service: props.row.original,
				showDetails,
				showTags,
			}),
		},
	];

    // Fonction de filtrage personnalisée avec Fuse.js
    const fuseFilter: FilterFn<Service> = (row, columnId, value, addMeta) => {
        if (!value || value === "" || columnId !== 'env') return false;

        console.log(row.original);

        return false;

        // Pour la recherche multi-termes, on utilise Fuse.js
        // if (value.includes(" ")) {
        //     let searches = value.split(" ");
        //     let matchesAll = true;
        //
        //     searches.forEach(searchTerm => {
        //         if (searchTerm.trim() !== "") {
        //             const result = fuse.search(searchTerm);
        //             const fuseServices = result.map(r => r.item);
        //             const serviceMatches = fuseServices.some(s => s.id === row.original.id);
        //             if (!serviceMatches) {
        //                 matchesAll = false;
        //             }
        //         }
        //     });
        //
        //     return matchesAll;
        // }

        // Pour la recherche simple, on utilise aussi Fuse.js
        const result = fuse.search(value);
        const fuseServices = result.map(r => r.item);
        return fuseServices.some(s => s.id === row.original.id);
    };

	// Configuration TanStack Table
	const tableOptions = writable<TableOptions<Service>>({
		data: [],
		columns,
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		globalFilterFn: fuseFilter,
		onSortingChange: sorting.set,
        enableColumnFilter: false
        // onColumnFiltersChange: columnFilters.set,
		// onGlobalFilterChange: (value) => {
		// 	globalFilter = typeof value === 'function' ? value(globalFilter) : value;
		// },
	});

	const table = createSvelteTable(tableOptions);

	// Synchronisation du globalFilter avec l'état de la table
	// $effect(() => {
	// 	if (globalFilter !== $table.getState().globalFilter) {
	// 		$table.setGlobalFilter(globalFilter);
	// 	}
	// });

	// Options d'affichages
	let frontWebOnly = $state(true);
	let openedOnly = $state(false);

	// Gestion de la recherche avec Fuse.js pour la recherche avancée
	const searchOptions: IFuseOptions<Service> = {
		includeScore: false,
		shouldSort: false,
		includeMatches: false,
		ignoreLocation: true,
		threshold: 0,
		keys: [
			"name",
			"id",
			"stack",
			"server",
			"urls",
			"placement",
			"env",
			"repository.image",
			"repository.imageDigest",
			"details.portail.webserviceUrl",
			"details.php.version",
			"details.symfony.version",
			"details.git.lastCommit",
		],
	};
	let fuse = new Fuse(services, searchOptions);

	// Lors de la recherche, on déselectionne tous les services sélectionnés
	// const handleSearch = debounce(e => {
	// 	const value = e.target.value;
	// 	searchValue = value;
	// 	$table.setGlobalFilter(value);
	// 	$selectedServices = [];
	// }, 150);

	const handleSearch = (e: any) => {
        $table.setGlobalFilter(String(e?.target?.value))
	};

	// Filtrage des données de base (avant TanStack Table)
	let baseFilteredServices = $derived.by(() => {
		let tmpServices = services;

		if (frontWebOnly) {
			tmpServices = tmpServices.filter(s => s.redeployedAllowed);
		}

		if (openedOnly) {
			tmpServices = tmpServices.filter(s => {
				return s.env !== "dev" && s.middleware === null && s?.details?.htaccess?.requireUser === undefined
			});
		}

		return tmpServices;
	});

	// Mise à jour des données de la table
	$effect(() => {
		tableOptions.update(options => ({
			...options,
			data: baseFilteredServices,
		}));
	});

	let isAllSelected = $derived($selectedServices.length === $table.getRowModel().rows.length && $table.getRowModel().rows.length > 0);
	const selectAll = () => {
		$selectedServices = isAllSelected ? [] : $table.getRowModel().rows.map(row => row.original);
	};

	// Gestion du comptage des éléments
	let tableData = $derived($table.getRowModel().rows.map(row => row.original));
	let countsServices = $derived(tableData.length);
	let countsServer = $derived(uniqBy(tableData, s => s.server).length);
	let countsStacks = $derived(uniqBy(tableData, s => s.stack).length);
	let countsTasks = $derived(sumBy(tableData, s => s.tasks.length));

	// Gestion du statut de la mise à jour
	let waitingIds = $derived($waitingForUpdates.filter(w => w.waiting).map(w => w.serviceId));
	let updatedIds = $derived($waitingForUpdates.filter(w => !w.waiting).map(w => w.serviceId));
	let hasErrorIds = $derived($waitingForUpdates.filter(w => w.error).map(w => w.serviceId));
</script>

{#if loadingErrors.length}
	<div class="alert alert-danger d-flex align-items-center" role="alert">
		<i class="fa fa-info-circle me-2"
		   data-bs-toggle="tooltip"
		   title="{loadingErrors[0].exception}"
		></i>
		<div>
			Une erreur s'est produite lors de la récupération des services {loadingErrors.length > 1 ? 'des clusters suivants' : 'du cluster suivant'} : {loadingErrors.map(e => e.server).join(', ')}
		</div>
	</div>
{/if}

<div class="table-responsive">
	<div class="gridjs gridjs-container" style="width: 100%">
		<div class="d-flex align-items-center justify-content-between mb-3">
			<div class="d-flex align-items-center" >
				{#if !loading}
					<div class="me-2">
						{countsServices} service{countsServices > 1 ? "s" : ""} -
						{countsServer} cluster{countsServer > 1 ? "s" : ""} -
						{countsStacks} stack{countsStacks > 1 ? "s" : ""} -
						{countsTasks} container{countsTasks > 1 ? "s" : ""}
					</div>
					{#if tableData.length > 0}
						<button
								class="btn btn-sm btn-info text-white me-2"
								onclick={preventDefault(() => selectAll())}
						>Tout {isAllSelected ? "désélectionner" : "sélectionner"}</button>
					{/if}
				{/if}
			</div>
			<div>
				<button
						class="btn btn-sm btn-info text-white"
						onclick={preventDefault(() => loadServices(true))}
						disabled="{$waitingForUpdates.length > 0}"
				>Mettre à jour la liste des services</button>
			</div>
		</div>
		<div class="d-flex align-items-center mb-3">
			<div class="gridjs-search me-2">
                <div class="input-group">
                    <input
                        bind:value={searchValue}
                        onkeyup={handleSearch}
                        type="text"
                        placeholder="Rechercher..."
                        class="gridjs-input gridjs-search-input"
                    />
                    {#if searchValue !== ""}
                        <span class="input-group-text"
                              type="button"
                              onclick={() => {
                                    searchValue = "";
                                    globalFilter = "";
                                    $table.setGlobalFilter("");
                                }}>
                                <i class="fa fa-times"></i>
                        </span>
                    {:else}
                        <span class="input-group-text">
                            <i class="fa fa-magnifying-glass"></i>
                        </span>
                    {/if}
                </div>
			</div>
			<div class="form-check form-switch me-2">
				<input
					class="form-check-input"
					type="checkbox"
					id="frontWebOnly"
					bind:checked={frontWebOnly}
				/>
				<label class="form-check-label" for="frontWebOnly"
					>Frontaux web uniquement</label
				>
			</div>
			<div class="form-check form-switch">
				<input
						class="form-check-input"
						type="checkbox"
						id="openedOnly"
						bind:checked={openedOnly}
				/>
				<label class="form-check-label" for="openedOnly"
				>Ouvert sur l'extérieur</label
				>
			</div>
			<div class="flex-grow-1"></div>
			<UpdateBar />
		</div>
		<div class="gridjs-wrapper" style="height: auto">
			<table
				role="grid"
				class="gridjs-table"
				style="min-width: 100%; height: auto"
			>
				<thead class="gridjs-head">
					{#each $table.getHeaderGroups() as headerGroup}
						<tr class="gridjs-tr">
							<th class="gridjs-th">
								<div class="gridjs-th-content"></div>
							</th>
							{#each headerGroup.headers as header}
								<th
									onclick={() => header.column.getCanSort() ? header.column.toggleSorting() : null}
									class="gridjs-th"
									class:gridjs-th-sort={header.column.getCanSort()}
								>
									<div class="gridjs-th-content">
                                        {@html header.column.columnDef.header}
									</div>
									{#if header.column.getCanSort()}
										<button
											title="Sort column"
											class="gridjs-sort gridjs-sort-{header.column.getIsSorted() === 'asc' ? 'asc' : header.column.getIsSorted() === 'desc' ? 'desc' : 'neutral'}"
										></button>
									{/if}
								</th>
							{/each}
						</tr>
					{/each}
				</thead>
				<tbody class="gridjs-tbody">
					{#if loading}
						<tr class="gridjs-tr">
							<td colspan="100%" class="gridjs-td ">
								<div class="d-flex justify-content-center">
									<div class="spinner-border text-info m-5" role="status">
										<span class="visually-hidden">Loading...</span>
									</div>
								</div>
							</td>
						</tr>
					{:else}
						{#each $table.getRowModel().rows as row (row.original.id)}
							<tr
								class="gridjs-tr"
								class:service-reloading={row.original.status.update === "updating" || waitingIds.includes(row.original.id)}
							>
								<td class="gridjs-td">
									{#if row.original.redeployedAllowed}
										<div class="text-center">
											{#if waitingIds.includes(row.original.id)}
												<i class="text-info fa-solid fa-circle-notch fa-spin"></i>
											{:else if hasErrorIds.includes(row.original.id)}
												<i class="text-danger fa-solid fa-square-exclamation"></i>
											{:else if updatedIds.includes(row.original.id)}
												<i class="text-success fa-solid fa-square-check"></i>
											{:else if $waitingForUpdates.length === 0}
												<input
													class="form-check-input"
													type="checkbox"
													bind:group={$selectedServices}
													value={row.original}
												/>
											{/if}
										</div>
									{/if}
								</td>
								{#each row.getVisibleCells() as cell}
                                    <td class="gridjs-td">
                                        {#if cell.column.columnDef.meta?.cellHTML}
                                            {@html cell.column.columnDef.cell(cell.getContext())}
                                        {:else}
                                            <svelte:component this={flexRender(cell.column.columnDef.cell, cell.getContext())}/>
                                        {/if}
                                    </td>
								{/each}
							</tr>
						{/each}
					{/if}
				</tbody>
			</table>
		</div>
	</div>
</div>

<CsvDownloadButton services={tableData} />

{#if tableData.length}
	<StatsTable services={tableData}/>
{/if}

<DetailsModal service={modalService} bind:isOpen={modalOpen} />
<RedeployModal service={tagModalService} bind:isOpen={tagModalOpen} />
